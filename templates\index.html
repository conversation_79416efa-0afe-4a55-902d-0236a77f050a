<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>共享单车免费骑</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
            margin: 0;
        }

        .container {
            background: transparent;
            padding: 60px 40px;
            text-align: center;
            width: 100%;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .bike-icon {
            font-size: 80px;
            margin-bottom: 30px;
            color: #4ecdc4;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .main-title {
            font-size: 42px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .subtitle {
            font-size: 16px;
            color: #7f8c8d;
            margin-bottom: 40px;
            line-height: 1.6;
        }

        .highlight {
            color: #e74c3c;
            font-weight: 600;
        }

        .cta-button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 18px 50px;
            font-size: 20px;
            font-weight: 600;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
            position: relative;
            overflow: hidden;
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .cta-button:hover::before {
            left: 100%;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(255, 107, 107, 0.4);
        }

        .cta-button:active {
            transform: translateY(-1px);
        }

        .features {
            display: flex;
            justify-content: space-around;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .feature {
            text-align: center;
            margin: 10px;
            flex: 1;
            min-width: 100px;
        }

        .feature-icon {
            font-size: 24px;
            margin-bottom: 8px;
            color: #4ecdc4;
        }

        .feature-text {
            font-size: 12px;
            color: #7f8c8d;
            font-weight: 500;
        }

        @media (max-width: 480px) {
            .container {
                padding: 40px 20px;
                min-height: 100vh;
            }

            .main-title {
                font-size: 32px;
            }

            .cta-button {
                padding: 15px 40px;
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="bike-icon">🚲</div>
        
        <h1 class="main-title">共享单车免费骑</h1>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">✅</div>
                <div class="feature-text">真实有效</div>
            </div>
            <div class="feature">
                <div class="feature-icon">🔒</div>
                <div class="feature-text">安全可靠</div>
            </div>
            <div class="feature">
                <div class="feature-icon">⚡</div>
                <div class="feature-text">即刻生效</div>
            </div>
        </div>
        
        <p class="subtitle">
            <span class="highlight">真实有效</span>，经过严格测试验证<br>
            最后测试日期：{{ test_date }}
        </p>
        
        <button class="cta-button" onclick="handleClick()">
            点击获取，即刻享受
        </button>
    </div>

    <script>
        function handleClick() {
            // 这里可以添加点击后的逻辑
            alert('功能开发中，敬请期待！');
        }

        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const button = document.querySelector('.cta-button');
            const container = document.querySelector('.container');
            
            // 移除3D效果，因为现在是全屏布局
        });
    </script>
</body>
</html>
