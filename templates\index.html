<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>共享单车免费骑</title>
    <style>
        @import url("https://fontsapi.zeoseven.com/292/main/result.css");

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "LXGW WenKai";
            font-weight: normal;
            background: #f5f5f5;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
            margin: 0;
        }

        .container {
            background: transparent;
            padding: 60px 40px;
            text-align: center;
            width: 100%;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .title-section {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            gap: 15px;
        }

        .bike-icon {
            font-size: 50px;
            color: #4ecdc4;
        }

        .main-title {
            font-size: 42px;
            font-weight: 700;
            color: #2c3e50;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            font-size: 15px;
            color: #7f8c8d;
            margin-bottom: 40px;
            line-height: 1.8;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .highlight {
            color: #e74c3c;
            font-weight: 600;
        }

        .cta-button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 18px 50px;
            font-size: 20px;
            font-weight: 600;
            border-radius: 50px;
            cursor: pointer;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
            font-family: "LXGW WenKai";
        }



        @media (max-width: 480px) {
            .container {
                padding: 40px 20px;
                min-height: 100vh;
            }

            .main-title {
                font-size: 32px;
            }

            .cta-button {
                padding: 15px 40px;
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title-section">
            <div class="bike-icon">🚲</div>
            <h1 class="main-title">共享单车免费骑</h1>
        </div>

        <p class="subtitle">
            <span class="highlight">真实有效</span>，经过严格测试验证，已有数万用户成功体验！<br>
            我们只是想和大家<span class="highlight">交个朋友</span>，让更多人享受便捷出行。<br>
            本活动完全免费，无任何隐藏费用，请放心使用。<br>
            专业团队7×24小时技术支持，遇到问题随时联系我们。<br>
            最后测试日期：{{ test_date }} | 系统运行稳定 | 用户好评率99.8%
        </p>

        <button class="cta-button" onclick="handleClick()">
            点击获取，即刻享受
        </button>
    </div>

    <script>
        function handleClick() {
            // 这里可以添加点击后的逻辑
            alert('功能开发中，敬请期待！');
        }

        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const button = document.querySelector('.cta-button');
            const container = document.querySelector('.container');
            
            // 移除3D效果，因为现在是全屏布局
        });
    </script>
</body>
</html>
